import os
import sys
from loguru import logger
from typing import Optional
from pathlib import Path
import contextvars
import logging

# 在模块导入时就设置拦截器，确保最早拦截
class EarlyInterceptHandler(logging.Handler):
    def emit(self, record):
        # 获取对应的loguru级别
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno
        
        # 找到调用者
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1
        
        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())

# 立即设置基础拦截器
logging.basicConfig(handlers=[EarlyInterceptHandler()], level=0, force=True)

# 强制替换所有已存在的logger的处理器
def force_intercept_all_loggers():
    """强制拦截所有logger"""
    # 清除root logger的所有处理器
    logging.getLogger().handlers.clear()
    logging.getLogger().addHandler(EarlyInterceptHandler())
    
    # 清除所有已存在的logger的处理器
    for name in logging.root.manager.loggerDict:
        logger_instance = logging.getLogger(name)
        logger_instance.handlers.clear()
        logger_instance.propagate = True

# 立即执行强制拦截
force_intercept_all_loggers()

request_id_var = contextvars.ContextVar('request_id', default='SYSTEM')

def request_id_filter(record):
    record["extra"]["request_id"] = request_id_var.get()
    return True

def setup_logger(
    level: str = "INFO",
    format_string: Optional[str] = None,
    file_path: Optional[str] = None,
    error_file_path: Optional[str] = None,
    rotation: str = "100 MB",
    retention: str = "30 days"
) -> None:
    """
    设置loguru日志器

    Args:
        level: 日志级别
        format_string: 日志格式
        file_path: 主日志文件路径（所有级别）
        error_file_path: 错误日志文件路径（ERROR及以上级别）
        rotation: 日志轮转大小
        retention: 日志保留时间
    """
    # 移除默认处理器
    logger.remove()
    
    # 恢复早期拦截器，让loguru接管
    try:
        from .early_intercept import early_interceptor
        early_interceptor.restore_logging()
    except ImportError:
        pass
    
    # 强制拦截所有logger，确保它们使用loguru
    force_intercept_all_loggers()
    
    # 检查是否在服务器环境（通过环境变量或配置判断）
    is_server_env = os.getenv("ENV_FOR_DYNACONF") in ["pre", "prod"] or os.getenv("CIRCUS_ENDPOINT") is not None
    
    # 默认格式 - 服务器环境禁用颜色
    if format_string is None:
        if is_server_env:
            # 服务器环境：无颜色格式
            format_string = (
                "{time:YYYY-MM-DD HH:mm:ss} | "
                "{level: <8} | "
                "{extra[request_id]} | "
                "{name}:{function}:{line} | "
                "{message}"
            )
        else:
            # 开发环境：带颜色格式
            format_string = (
                "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                "<level>{level: <8}</level> | "
                "<magenta>{extra[request_id]}</magenta> | "
                "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                "<level>{message}</level>"
            )
    
    # 添加控制台处理器
    logger.add(
        sys.stdout,
        format=format_string,
        filter=request_id_filter,
        level=level,
        colorize=not is_server_env,  # 服务器环境禁用颜色
        backtrace=True,
        diagnose=True
    )
    
    # 添加主日志文件处理器（所有级别）
    if file_path:
        # 确保日志目录存在
        log_dir = Path(file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        logger.add(
            file_path,
            format=format_string,
            filter=request_id_filter,
            level=level,
            rotation=rotation,
            retention=retention,
            compression="zip",
            backtrace=True,
            diagnose=True
        )

    # 添加错误日志文件处理器（仅ERROR及以上级别）
    if error_file_path:
        # 确保错误日志目录存在
        error_log_dir = Path(error_file_path).parent
        error_log_dir.mkdir(parents=True, exist_ok=True)

        logger.add(
            error_file_path,
            format=format_string,
            filter=request_id_filter,
            level="ERROR",  # 只记录ERROR及以上级别
            rotation=rotation,
            retention=retention,
            compression="zip",
            backtrace=True,
            diagnose=True
        )
    

def get_logger(name: str = "alpha-service"):
    """获取日志器实例"""
    return logger.bind(name=name, request_id=request_id_var.get())

def init_logger_with_config():
    """使用配置初始化日志"""
    try:
        # 直接使用 dynaconf 配置
        from ..config.environments import env_manager

        config = env_manager.get_config()

        # 生成错误日志文件路径
        main_log_path = config.log_file_path
        if main_log_path:
            # 将 application.log 改为 error.log
            error_log_path = main_log_path.replace('application.log', 'error.log')
            # 如果没有找到 application.log，则在同目录下创建 error.log
            if error_log_path == main_log_path:
                log_dir = Path(main_log_path).parent
                error_log_path = str(log_dir / "error.log")
        else:
            error_log_path = None

        setup_logger(
            level=config.log_level,
            file_path=main_log_path,
            error_file_path=error_log_path,
            rotation="100 MB",
            retention="30 days"
        )

        log_info = f"📝 日志已初始化 - 环境: {env_manager.current_env.value}, 级别: {config.log_level}"
        if main_log_path:
            log_info += f", 主日志: {main_log_path}"
        if error_log_path:
            log_info += f", 错误日志: {error_log_path}"
        logger.info(log_info)

    except Exception as e:
        # 如果配置加载失败，使用默认配置
        print(f"警告: 日志配置加载失败，使用默认配置: {e}")
        setup_logger(
            level="INFO",
            file_path="logs/application.log",
            error_file_path="logs/error.log"
        )

class RequestContext:
    """请求上下文管理器"""
    def __init__(self, rid: str = None):
        self.token = None
        self.request_id = rid

    def __enter__(self):
        self.token = request_id_var.set(self.request_id)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        request_id_var.reset(self.token)

# 初始化日志配置
init_logger_with_config() 